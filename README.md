# Climate Governance Service

A minimalistic message queue-based service for processing climate governance data.

## Architecture

- **Stateless**: No database, pure message queue processing
- **Event-driven**: All communication through RabbitMQ
- **Containerized**: Docker and Docker Compose setup
- **Scalable**: Horizontal scaling via queue consumers

## Components

- **Data Processor**: Processes raw climate data
- **Analytics Processor**: Aggregates results and generates reports
- **Message Queue**: RabbitMQ for all communication
- **Data Loader**: Loads data from colab.md file

## Quick Start

1. **Start the services:**
   ```bash
   docker-compose up -d
   ```

2. **Check RabbitMQ Management UI:**
   - URL: http://localhost:15672
   - Username: admin
   - Password: admin123

3. **Load climate data:**
   ```bash
   docker-compose exec climate-service python app/data_loader.py
   ```

4. **View logs:**
   ```bash
   docker-compose logs -f climate-service
   ```

## Data Flow

```
colab.md → Data Loader → Raw Data Queue → Data Processor → Processed Data Queue → Analytics Processor → Analytics Queue
```

## Message Queues

- `climate.raw.data` - Raw climate data input
- `climate.processed.data` - Processed data results
- `climate.analytics` - Analytics reports
- `climate.notifications` - System notifications

## Data Types

- Coordination Mechanisms
- Processes
- Feedback Mechanisms
- Monitoring Indicators
- Community Satisfaction
- Project Resilience

## Development

1. **Local development:**
   ```bash
   pip install -r requirements.txt
   python app/main.py
   ```

2. **Run data loader:**
   ```bash
   python app/data_loader.py
   ```

## Configuration

Environment variables in `.env`:
- `RABBITMQ_URL` - RabbitMQ connection URL
- `LOG_LEVEL` - Logging level (INFO, DEBUG, etc.)

## Monitoring

- RabbitMQ Management UI for queue monitoring
- Application logs for processing status
- Built-in notification system for alerts

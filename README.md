# RMT Service

A minimalistic message queue-based service for processing climate governance data.

## Architecture

- **Stateless**: No database, pure message queue processing
- **Event-driven**: All communication through RabbitMQ with single queue pattern
- **Containerized**: Docker and Docker Compose setup
- **Scalable**: Horizontal scaling via queue consumers

## Components

- **Data Processor**: Processes raw climate data
- **Analytics Processor**: Aggregates results and generates reports
- **Message Queue**: RabbitMQ for all communication
- **Data Loader**: Loads data from Python data files in `/data` folder

## Quick Start

1. **Start the services:**
   ```bash
   docker-compose up -d
   ```

2. **Check RabbitMQ Management UI:**
   - URL: http://localhost:15672
   - Username: admin
   - Password: admin123

3. **Load climate data:**
   ```bash
   docker-compose exec rmt-service python app/data_loader.py
   ```

4. **View logs:**
   ```bash
   docker-compose logs -f rmt-service
   ```

## Data Flow

```
data/*.py → Data Loader → Single Queue (rmt.events) → Event Router → Data Processor → Analytics Processor
```

## Message Queue Architecture

- **Single Queue**: `rmt.events` - All events with type-based routing
- **Event Types**:
  - `data_received` - Raw climate data input
  - `data_processed` - Processed data results
  - `analytics_generated` - Analytics reports
  - `notification` - System notifications

## Data Types

The service processes the following climate governance data types from `/data` folder:

- **Coordination Mechanisms** (`data/coordination_mechanisms.py`)
- **Processes** (`data/processes.py`)
- **Feedback Mechanisms** (`data/feedback_mechanisms.py`)
- **Monitoring Indicators** (`data/monitoring_indicators.py`)
- **Community Satisfaction** (`data/community_satisfaction.py`)
- **Project Resilience** (`data/improved_project_resilience.py`)
- **Stakeholders** (`data/stakeholders.py`)
- **Socio-Ecological Challenges** (`data/socio_ecological_challenges.py`)
- **Governance Rules** (`data/governance_rules.py`)

## Development

1. **Local development:**
   ```bash
   pip install -r requirements.txt
   python app/main.py
   ```

2. **Run data loader:**
   ```bash
   python app/data_loader.py
   ```

## Configuration

Environment variables in `.env`:
- `RABBITMQ_URL` - RabbitMQ connection URL
- `LOG_LEVEL` - Logging level (INFO, DEBUG, etc.)

## Monitoring

- RabbitMQ Management UI for queue monitoring
- Application logs for processing status
- Built-in notification system for alerts

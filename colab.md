# Install Hugging Face Transformers
!pip install transformers

# Install PyTorch (if not already installed)
!pip install torch
!pip install transformers pdfplumber python-docx


coordination_mechanisms = [
    ("Community Decision Centers", "Facilitate local engagement and collect feedback."),
    ("Municipal Planning Council", "Applies the Climate Check to projects during review processes."),
    ("Independent Expert Panel", "Designs the checklist and evaluation criteria."),
    ("Public-Private Partnerships", "Collaborate between government and businesses for sustainable projects."),
    ("Interagency Climate Task Force", "Ensures coordination between environmental, transportation, and housing agencies."),
    ("Climate Advocacy Groups", "Raise awareness and push for climate-conscious policies."),
    ("Stakeholder Roundtables", "Gather diverse groups to discuss project impact and concerns."),
    ("Sustainability Advisory Boards", "Guide urban development decisions with a focus on sustainability."),
    ("Green Infrastructure Committees", "Ensure projects incorporate green infrastructure solutions."),
    ("Community Climate Forums", "Allow residents to voice concerns and suggest improvements."),
    ("Regional Climate Planning Networks", "Coordinate policies across neighboring municipalities."),
    ("Climate Resilience Planning Units", "Develop long-term adaptation strategies."),
    ("Technical Working Groups", "Bring together experts for research-driven project evaluations."),
    ("Sustainable Development Committees", "Advise on balancing development with environmental concerns."),
    ("Eco-Certification Review Panels", "Assess projects for sustainability certification."),
    ("Urban Resilience Offices", "Address climate risks in city planning."),
    ("Climate Justice Coalitions", "Ensure climate policies are equitable and inclusive."),
    ("Environmental Impact Review Boards", "Assess and mitigate negative environmental impacts."),
    ("Smart Growth Task Forces", "Promote compact, transit-oriented development."),
    ("Integrated Water Management Teams", "Coordinate efforts to manage stormwater and flooding."),
    ("Energy Transition Councils", "Plan the shift towards renewable energy sources."),
    ("Disaster Risk Reduction Committees", "Develop strategies to minimize climate-related disasters."),
    ("Coastal Protection Task Forces", "Focus on protecting coastal communities from rising sea levels."),
    ("Climate Data Sharing Networks", "Ensure stakeholders have access to accurate climate information."),
    ("Participatory Budgeting Groups", "Allow communities to decide on climate-related investments."),
    ("Environmental Mediation Panels", "Resolve conflicts between developers and environmental advocates."),
    ("Low-Carbon Transport Committees", "Promote sustainable transportation options."),
    ("Municipal Climate Innovation Hubs", "Support climate-friendly urban innovation."),
    ("Adaptation and Mitigation Councils", "Plan for both reducing and adapting to climate change."),
    ("Nature-Based Solutions Working Groups", "Encourage the use of ecological approaches in projects."),
    ("Biodiversity Conservation Councils", "Ensure projects protect local biodiversity."),
    ("Sustainable Agriculture Boards", "Support climate-conscious food production policies."),
    ("Urban Greening Initiatives", "Expand urban forests and green spaces."),
    ("Resilient Housing Task Forces", "Ensure climate-conscious housing policies."),
    ("Climate Education and Outreach Teams", "Inform the public about climate policies and solutions."),
    ("Carbon Offset Committees", "Oversee carbon credit programs."),
    ("Circular Economy Action Groups", "Promote waste reduction and sustainable resource use."),
    ("Zero-Emissions Planning Councils", "Create strategies to achieve net-zero emissions."),
    ("Renewable Energy Development Panels", "Oversee solar, wind, and other renewable energy projects."),
    ("Eco-Tourism Coordination Boards", "Balance tourism with sustainability."),
    ("Flood Resilience Working Groups", "Develop policies to reduce flood risks."),
    ("Air Quality Management Panels", "Address urban air pollution challenges."),
    ("Forest Conservation Committees", "Protect and restore local forests."),
    ("Urban Heat Island Task Forces", "Implement strategies to reduce urban heat effects."),
    ("Hydrology and Climate Science Committees", "Integrate water and climate science into planning."),
    ("Green Technology Assessment Panels", "Evaluate new technologies for sustainability."),
    ("Inclusive Climate Governance Councils", "Ensure all voices are included in climate decision-making."),
    ("Water Security Task Forces", "Develop strategies to ensure long-term water availability."),
    ("Carbon Pricing Committees", "Implement pricing mechanisms to reduce carbon emissions."),
    ("Resilient Infrastructure Coordination Groups", "Ensure climate-conscious infrastructure development."),
]
processes = [
    ("Integrate the Climate Check into existing project approval workflows.", "Ensure climate considerations are part of every approval process."),
    ("Host regular consultations between stakeholders to refine the tool.", "Gather feedback to continuously improve the Climate Check framework."),
    ("Develop a standardized climate impact assessment form.", "Ensure all projects are evaluated using a consistent methodology."),
    ("Establish a centralized data repository for climate metrics.", "Allow for transparent tracking of climate-related project data."),
    ("Create climate risk mapping for project planning.", "Identify high-risk areas and prevent unsustainable development."),
    ("Require life-cycle carbon assessments for all projects.", "Evaluate total emissions from construction to demolition."),
    ("Incorporate scenario-based climate modeling into approvals.", "Assess long-term project viability under different climate conditions."),
    ("Mandate renewable energy integration in infrastructure planning.", "Ensure sustainable energy sources are prioritized."),
    ("Adopt digital twin technology for urban climate simulations.", "Use AI-driven models to predict environmental impacts."),
    ("Enforce environmental mitigation plans as project prerequisites.", "Ensure proactive steps to offset project-related impacts."),
    ("Conduct annual climate resilience audits for infrastructure.", "Evaluate the ability of structures to withstand climate changes."),
    ("Create a participatory budgeting framework for climate projects.", "Allow local communities to decide how climate funds are allocated."),
    ("Introduce a climate-conscious procurement policy.", "Prioritize vendors with strong sustainability commitments."),
    ("Establish independent review panels for climate-sensitive projects.", "Ensure unbiased assessments of high-impact proposals."),
    ("Set up a certification system for climate-aligned projects.", "Provide official recognition for environmentally sound initiatives."),
    ("Develop AI-driven monitoring tools for climate compliance.", "Use technology to track and enforce sustainability commitments."),
    ("Require biodiversity impact assessments before approvals.", "Protect ecosystems by assessing potential damage in advance."),
    ("Ensure multi-sector coordination for climate policy execution.", "Foster collaboration across transportation, energy, and urban planning."),
    ("Incentivize nature-based solutions in development projects.", "Encourage the use of wetlands, forests, and natural barriers."),
    ("Establish a climate action task force for urban expansion.", "Ensure city growth aligns with sustainability goals."),
    ("Mandate climate adaptation plans for all new developments.", "Ensure projects are built with resilience to future changes."),
    ("Host bi-annual climate policy review meetings.", "Regularly update regulations to reflect scientific advancements."),
    ("Require all projects to align with the Paris Agreement.", "Ensure compliance with global climate commitments."),
    ("Expand public participation in climate governance.", "Enhance transparency by involving citizens in decision-making."),
    ("Develop climate-sensitive economic incentives.", "Offer tax breaks for green construction and retrofitting."),
    ("Require flood risk assessments for urban projects.", "Prevent construction in areas prone to rising water levels."),
    ("Introduce an emissions benchmarking system for industries.", "Compare sectoral emissions and drive improvements."),
    ("Ensure transparency in climate impact reporting.", "Require all projects to publicly disclose sustainability data."),
    ("Implement a carbon credit trading system.", "Allow projects to offset emissions through verified credits."),
    ("Introduce an eco-labeling program for construction materials.", "Promote the use of sustainable building materials."),
    ("Develop educational programs on climate-friendly urban planning.", "Train future leaders on sustainable infrastructure."),
    ("Monitor deforestation rates linked to urban expansion.", "Track and mitigate forest loss from development projects."),
    ("Apply performance-based sustainability standards in approvals.", "Evaluate projects based on environmental outcomes."),
    ("Foster cross-border collaboration on climate resilience.", "Work with neighboring regions to strengthen adaptation efforts."),
    ("Integrate blue-green infrastructure in urban designs.", "Use water-based and vegetative solutions to combat climate risks."),
    ("Develop a public-facing climate project dashboard.", "Increase accountability by visualizing project impacts."),
    ("Require all government-funded projects to be carbon neutral.", "Ensure taxpayer dollars support sustainable initiatives."),
    ("Host climate impact hackathons for policy innovation.", "Encourage creative solutions for emerging environmental challenges."),
    ("Ensure marginalized communities are consulted in climate planning.", "Promote equity in environmental decision-making."),
    ("Introduce adaptive land-use zoning for climate-sensitive regions.", "Prevent risky development in vulnerable areas."),
    ("Develop an early warning system for climate-induced disasters.", "Improve preparedness for extreme weather events."),
    ("Align infrastructure investments with net-zero targets.", "Ensure financial decisions support emissions reductions."),
    ("Strengthen green financing mechanisms.", "Expand funding options for climate-aligned projects."),
    ("Establish regional water security plans.", "Ensure long-term access to clean water in climate-stressed areas."),
    ("Expand tree canopy coverage in urban centers.", "Reduce heat islands by planting more trees."),
    ("Introduce smart grid technology for resilient energy distribution.", "Modernize power systems to adapt to changing conditions."),
    ("Develop digital carbon tracking systems.", "Use blockchain or AI to verify emissions reductions."),
    ("Mandate rooftop solar installation on new buildings.", "Increase renewable energy adoption in cities."),
    ("Standardize impact metrics for climate-smart projects.", "Ensure comparability of sustainability performance."),
    ("Pilot carbon-negative construction techniques.", "Explore new methods for removing CO2 from the atmosphere."),
    ("Strengthen coastal zone regulations.", "Limit development that exacerbates shoreline erosion."),
    ("Expand sustainable transport networks.", "Increase investments in public transit and bike lanes."),
    ("Integrate AI-driven weather forecasting into planning.", "Improve risk prediction for climate-sensitive projects."),
    ("Require green space allocation in all new urban projects.", "Ensure cities remain livable and biodiverse."),
    ("Mandate stormwater management best practices.", "Reduce flooding risks through improved urban drainage."),
    ("Develop circular economy strategies for construction waste.", "Promote recycling and reuse in building projects."),
    ("Expand subsidies for community-led climate initiatives.", "Empower local groups to drive sustainable change."),
    ("Create green workforce development programs.", "Train workers in emerging climate-related industries."),
    ("Ensure all projects align with the UN Sustainable Development Goals.", "Integrate global sustainability objectives into planning."),
    ("Develop impact fee structures for high-emission projects.", "Charge developers based on their environmental footprint."),
    ("Host annual climate action summits for city planners.", "Encourage knowledge-sharing among urban policymakers."),
    ("Expand electric vehicle infrastructure in urban areas.", "Increase charging station availability."),
    ("Pilot floating city concepts in flood-prone regions.", "Explore adaptive architecture for rising sea levels."),
    ("Strengthen methane reduction policies in waste management.", "Address non-CO2 greenhouse gas emissions."),
    ("Develop heatwave preparedness strategies.", "Reduce health risks associated with extreme temperatures."),
    ("Expand research funding for climate mitigation strategies.", "Support innovation in carbon reduction."),
    ("Require soil health assessments before land-use changes.", "Preserve soil integrity for future generations."),
    ("Develop adaptive governance models for climate resilience.", "Make policies flexible to respond to evolving challenges."),
]
feedback_mechanisms = [
    ("Use monitoring data to refine the checklist and evaluation processes", "Ensures continuous improvement of climate assessment tools."),
    ("Regularly update the Climate Check criteria based on emerging climate challenges", "Keeps the evaluation process relevant to new climate risks."),
    ("Conduct annual stakeholder workshops for feedback", "Gathers insights from community members, policymakers, and experts."),
    ("Establish an online feedback portal for public input", "Allows continuous feedback from citizens on climate policies."),
    ("Perform quarterly project reviews based on monitoring indicators", "Ensures projects stay aligned with climate goals."),
    ("Develop a community advisory board for project oversight", "Engages local leaders in reviewing and improving sustainability efforts."),
    ("Use AI-driven sentiment analysis to assess public opinion", "Monitors public attitudes toward climate initiatives in real time."),
    ("Hold public hearings before approving major projects", "Encourages transparency and inclusivity in decision-making."),
    ("Create a rating system for climate resilience in projects", "Provides an easy-to-understand evaluation of sustainability performance."),
    ("Conduct post-project impact assessments", "Evaluates real-world effectiveness of climate adaptation strategies."),
    ("Encourage whistleblower reporting for non-compliance", "Ensures accountability in meeting climate commitments."),
    ("Implement a climate performance scorecard for municipalities", "Allows benchmarking and comparisons of sustainability efforts."),
    ("Use citizen science programs to collect climate impact data", "Engages the public in monitoring local environmental changes."),
    ("Require annual sustainability reports from developers", "Ensures project owners document climate-conscious efforts."),
    ("Conduct independent third-party audits of climate policies", "Verifies compliance and prevents biased self-assessments."),
    ("Use mobile apps to collect real-time environmental data", "Leverages technology for on-the-ground climate monitoring."),
    ("Integrate climate resilience feedback into urban planning models", "Improves future city designs based on past project learnings."),
    ("Host climate hackathons to crowdsource innovative solutions", "Engages the public and experts in improving climate policies."),
    ("Publish transparency reports on climate project performance", "Increases public awareness of government and developer actions."),
    ("Create a complaint resolution system for climate-related concerns", "Allows citizens to report environmental and resilience issues."),
    ("Develop a reward system for best-performing climate projects", "Encourages competition and innovation in sustainability."),
    ("Set up a hotline for reporting climate risks and non-compliance", "Provides immediate response channels for community concerns."),
    ("Use blockchain for immutable climate data recording", "Ensures transparency and prevents tampering with environmental reports."),
    ("Adopt machine learning models for predictive climate risk feedback", "Uses data analytics to refine urban planning and project evaluation."),
    ("Create a peer-review process among municipalities for policy refinement", "Encourages knowledge-sharing and collaborative improvements."),
    ("Mandate climate training for project evaluators", "Ensures officials understand evolving sustainability challenges."),
    ("Establish real-time dashboards for public climate data access", "Allows citizens to track project progress and environmental impact."),
    ("Encourage feedback loops in corporate sustainability policies", "Pushes businesses to refine their sustainability commitments."),
    ("Partner with universities for independent research-based assessments", "Ensures scientifically backed improvements to climate policies."),
    ("Set up an expert panel to review feedback and propose updates", "Adds credibility and depth to climate evaluation processes."),
    ("Use gamification to engage the public in climate data collection", "Increases community participation in environmental monitoring."),
    ("Require developers to submit lessons learned reports", "Ensures continuous improvement from past projects."),
    ("Hold biannual town hall meetings focused on climate progress", "Maintains ongoing dialogue with residents and stakeholders."),
    ("Create an open-source climate adaptation database", "Allows global sharing of best practices and project outcomes."),
    ("Develop climate risk simulation tools for community engagement", "Helps residents visualize climate challenges and potential solutions."),
    ("Offer financial incentives for projects that incorporate feedback", "Encourages adaptive and responsive climate-conscious development."),
    ("Implement a structured grievance mechanism for affected communities", "Provides an official channel for addressing climate concerns."),
    ("Utilize social media sentiment tracking on climate policies", "Monitors public reactions and adapts policies accordingly."),
    ("Establish mentorship programs between successful and new climate projects", "Fosters knowledge-sharing to improve future developments."),
    ("Adopt a participatory budgeting approach for climate investments", "Engages communities in deciding sustainability priorities."),
    ("Create dynamic climate scenario models to guide future projects", "Uses past project feedback to refine future climate assessments."),
    ("Publish annual climate action reports for accountability", "Keeps the public informed about government and private sector progress."),
    ("Encourage real-time data collection via IoT sensors", "Improves accuracy in climate monitoring and feedback mechanisms."),
    ("Develop an interactive public dashboard displaying climate project updates", "Enhances transparency and community involvement."),
    ("Collaborate with indigenous communities for climate adaptation insights", "Incorporates traditional ecological knowledge into policy refinements."),
    ("Use predictive analytics to adjust urban planning strategies", "Refines land-use decisions based on climate resilience feedback."),
    ("Provide public recognition for citizen contributions to climate action", "Encourages grassroots involvement in sustainability."),
    ("Implement neighborhood-based climate monitoring programs", "Decentralizes feedback collection for hyper-local insights."),
    ("Create a sustainability benchmarking framework for industries", "Provides sector-specific performance evaluations."),
    ("Develop risk maps based on climate feedback data", "Improves disaster preparedness and land-use planning."),
    ("Use drone technology for rapid environmental assessments", "Enables faster and more precise climate risk evaluations."),
    ("Mandate annual climate adaptation plan updates based on feedback", "Ensures continuous policy evolution."),
    ("Integrate real-world feedback into AI-powered climate forecasting", "Enhances predictive models for future climate impacts."),
    ("Adopt a rolling review process for climate resilience policies", "Allows for incremental improvements instead of waiting for periodic updates."),
    ("Set up a dedicated climate hotline for emergency responses", "Improves disaster preparedness and quick feedback integration."),
    ("Encourage businesses to conduct employee-driven climate evaluations", "Engages workforces in improving corporate sustainability efforts."),
    ("Use crowdsourcing platforms to gather urban heat data", "Expands citizen participation in monitoring local climate trends."),
    ("Develop a training module for public officials on feedback implementation", "Ensures government responsiveness to climate concerns."),
    ("Require mandatory climate resilience debriefings post-disasters", "Improves learning from real-world extreme weather events."),
    ("Incentivize green startups to refine climate resilience strategies", "Supports innovation in sustainability sectors."),
    ("Create a reward system for community-based climate monitoring efforts", "Encourages local participation in data collection."),
    ("Use satellite data verification for reported environmental progress", "Prevents misinformation in sustainability reporting."),
    ("Launch pilot projects to test feedback-driven climate solutions", "Ensures data-driven policy adjustments."),
    ("Establish school-based climate clubs to collect student feedback", "Engages younger generations in sustainability initiatives."),
    ("Use augmented reality to visualize urban climate risks", "Improves public understanding of potential environmental threats."),
    ("Create an open-access climate feedback research archive", "Facilitates global knowledge-sharing on resilience strategies."),
    ("Develop regional climate resilience scorecards", "Provides comparative insights into local adaptation efforts."),
    ("Use artificial intelligence for automated feedback analysis", "Enhances processing and response times to community concerns."),
    ("Publish failure case studies to learn from past climate mistakes", "Encourages transparency and continuous improvement."),
    ("Host international climate feedback forums for shared learning", "Promotes cross-border cooperation on sustainability issues."),
    ("Develop performance-based incentives for municipalities meeting feedback goals", "Encourages continuous adaptation to climate challenges."),
    ("Require real-world pilot testing of new climate policies", "Ensures policies are effective before full-scale implementation."),
    ("Set up a global network of climate feedback hubs", "Encourages international collaboration on adaptation efforts."),
]
monitoring_indicators = [
    ("Percentage of projects passing the Climate Check", "Measures the proportion of developments meeting climate-conscious criteria."),
    ("Reduction in urban flooding and heat-related risks", "Assesses the effectiveness of resilience measures in mitigating climate risks."),
    ("Community satisfaction scores from public consultations", "Evaluates public perception of climate-conscious projects."),
    ("Number of green infrastructure projects implemented", "Tracks the adoption of parks, green roofs, and sustainable drainage."),
    ("Percentage reduction in carbon emissions from developments", "Monitors how projects contribute to net-zero targets."),
    ("Compliance rate with local climate regulations", "Measures adherence to climate protection policies."),
    ("Percentage increase in permeable surface area", "Indicates improvements in stormwater absorption and flood mitigation."),
    ("Reduction in energy consumption per building", "Assesses the impact of efficiency upgrades in urban planning."),
    ("Increase in renewable energy adoption", "Measures the percentage of projects incorporating solar, wind, or other renewables."),
    ("Reduction in transportation-related emissions", "Tracks the effectiveness of sustainable mobility initiatives."),
    ("Number of buildings achieving green certification (e.g., LEED, BREEAM)", "Indicates commitment to sustainable construction standards."),
    ("Change in urban heat island intensity", "Evaluates the impact of green spaces and reflective materials."),
    ("Percentage of flood-prone areas protected by mitigation measures", "Assesses resilience against extreme weather events."),
    ("Water conservation improvements in new developments", "Measures reductions in water consumption through efficiency measures."),
    ("Biodiversity index in urban areas", "Monitors ecosystem health in developed zones."),
    ("Increase in public transit ridership", "Tracks shifts from car dependency to sustainable transport."),
    ("Reduction in waste generation from construction projects", "Monitors sustainable resource use and circular economy practices."),
    ("Number of projects integrating nature-based solutions", "Assesses commitment to ecological adaptation strategies."),
    ("Community participation rate in climate planning processes", "Evaluates engagement and inclusivity in decision-making."),
    ("Percentage of infrastructure projects designed for climate resilience", "Tracks adaptation of roads, bridges, and utilities to climate risks."),
    ("Reduction in air pollution levels", "Measures improvements in urban air quality from sustainable policies."),
    ("Number of climate risk assessments conducted before project approvals", "Ensures proactive evaluation of vulnerabilities."),
    ("Increase in tree canopy cover", "Monitors progress in urban greening efforts."),
    ("Reduction in disaster recovery costs", "Tracks the economic benefits of proactive resilience measures."),
    ("Percentage of households with access to climate-resilient housing", "Measures equity in adaptation efforts."),
    ("Number of adaptive reuse projects reducing urban sprawl", "Assesses the effectiveness of repurposing old buildings."),
    ("Increase in use of passive cooling designs", "Tracks energy-efficient architectural trends."),
    ("Proportion of city budget allocated to climate adaptation", "Monitors financial commitment to sustainability."),
    ("Improvement in groundwater recharge rates", "Assesses effectiveness of rainwater harvesting and permeable landscapes."),
    ("Reduction in impervious surface coverage", "Monitors changes in urban planning to improve water absorption."),
    ("Increase in electric vehicle infrastructure", "Tracks investments in sustainable transportation."),
    ("Implementation of carbon pricing mechanisms", "Measures the impact of financial incentives for emission reductions."),
    ("Reduction in traffic congestion", "Monitors efficiency of mobility planning and public transit."),
    ("Increase in adoption of circular economy principles", "Tracks reductions in waste and material reuse rates."),
    ("Percentage of developments incorporating smart grid technology", "Measures progress in energy resilience."),
    ("Increase in urban farming initiatives", "Monitors sustainability in food production within cities."),
    ("Reduction in building cooling demand", "Assesses the impact of passive design and efficient insulation."),
    ("Increase in climate risk literacy among decision-makers", "Tracks training programs for officials and stakeholders."),
    ("Reduction in potable water use per capita", "Measures efficiency in water conservation policies."),
    ("Increase in decentralized energy production", "Tracks local microgrid and community solar adoption."),
    ("Extent of flood zone mapping and hazard modeling updates", "Measures improvements in climate risk assessment."),
    ("Number of businesses adopting sustainable supply chains", "Tracks corporate contributions to climate goals."),
    ("Growth in financial incentives for climate-resilient projects", "Monitors government support for sustainable investments."),
    ("Reduction in construction material waste", "Assesses effectiveness of sustainable building practices."),
    ("Increase in adoption of resilient urban design", "Tracks climate-conscious planning approaches."),
    ("Increase in use of permeable pavement", "Monitors implementation of flood mitigation measures."),
    ("Reduction in vehicle miles traveled (VMT)", "Tracks shift toward more sustainable commuting behaviors."),
    ("Number of resilience hubs established in vulnerable communities", "Assesses preparedness for extreme climate events."),
    ("Increase in access to climate insurance programs", "Monitors financial preparedness for disaster recovery."),
    ("Implementation of zoning policies favoring mixed-use, walkable neighborhoods", "Tracks shifts in land use for sustainability."),
    ("Reduction in urban noise pollution", "Measures improvements in livability through sustainable design."),
    ("Expansion of regional climate adaptation partnerships", "Tracks collaboration across jurisdictions for resilience."),
    ("Reduction in disaster displacement rates", "Monitors effectiveness of adaptation strategies in protecting residents."),
    ("Increase in green job creation", "Measures economic benefits of climate-conscious projects."),
    ("Implementation of nature-positive construction materials", "Tracks shifts toward sustainable resource extraction."),
    ("Expansion of heatwave early warning systems", "Assesses preparedness for extreme temperature events."),
    ("Increase in pedestrian and cyclist-friendly infrastructure", "Monitors prioritization of non-motorized transport."),
    ("Percentage of city-owned buildings retrofitted for energy efficiency", "Tracks public sector leadership in climate adaptation."),
    ("Reduction in water contamination incidents", "Measures improvements in pollution control efforts."),
    ("Implementation of reforestation and afforestation initiatives", "Tracks carbon sequestration progress."),
    ("Increase in data-driven climate resilience planning", "Monitors use of predictive analytics for decision-making."),
    ("Reduction in reliance on fossil fuel-based heating systems", "Tracks transitions to renewable-based heating solutions."),
    ("Number of urban wetlands restored", "Monitors ecological health and flood mitigation progress."),
    ("Increase in local food system resilience", "Tracks availability of sustainable food sources."),
    ("Reduction in climate-induced migration rates", "Monitors effectiveness of adaptation in reducing displacement."),
    ("Implementation of climate-responsive building codes", "Ensures new developments are future-proof."),
    ("Increase in heat-reflective roofing adoption", "Tracks mitigation of urban heat island effects."),
    ("Expansion of blue-green infrastructure networks", "Monitors integration of water and vegetation-based solutions."),
    ("Increase in corporate sustainability disclosures", "Tracks business accountability in climate action."),
    ("Reduction in sewer overflows during extreme weather", "Measures resilience of wastewater infrastructure."),
    ("Increase in community-led climate initiatives", "Monitors local engagement in sustainability efforts."),
    ("Expansion of low-carbon public procurement policies", "Tracks government commitment to sustainable sourcing."),
    ("Increase in cross-sector collaboration on climate resilience", "Measures multi-stakeholder engagement in adaptation."),
    ("Reduction in exposure to extreme weather events", "Tracks success of risk mitigation measures."),
    ("Increase in adoption of AI-driven climate monitoring", "Monitors technological innovation in sustainability efforts."),
    ("Expansion of sustainable cooling strategies in public spaces", "Measures efforts to protect vulnerable populations."),
    ("Increase in land area designated for conservation", "Monitors ecosystem protection policies."),
    ("Reduction in untreated stormwater runoff", "Tracks improvements in drainage and water management."),
    ("Expansion of high-speed rail and other low-carbon transport options", "Measures shifts toward sustainable mobility."),
    ("Increase in waste-to-energy project implementation", "Tracks circular economy innovation."),
    ("Growth in small-scale renewable energy projects", "Monitors decentralized clean energy adoption."),
    ("Reduction in heat stress-related illnesses", "Measures success of urban cooling strategies."),
    ("Increase in financing for climate adaptation research", "Tracks investment in long-term resilience solutions."),
    ("Expansion of climate equity initiatives", "Ensures vulnerable communities receive targeted support."),
]
community_satisfaction = [
    ("Transparent decision-making processes increase public trust.", "Engaging stakeholders in open discussions about climate-related projects."),
    ("Public participation in climate planning.", "Allowing communities to voice concerns and provide input on climate initiatives."),
    ("Improved access to green spaces.", "Enhancing quality of life through parks, urban forests, and recreational areas."),
    ("Reduction in air and noise pollution.", "Creating healthier living conditions for residents."),
    ("Fair distribution of climate adaptation resources.", "Ensuring that all community members benefit equitably."),
    ("Increased affordability of sustainable housing.", "Providing energy-efficient, climate-friendly housing options for all."),
    ("Job creation in green industries.", "Developing employment opportunities in renewable energy, conservation, and sustainability sectors."),
    ("Stronger community engagement in decision-making.", "Ensuring residents have a say in local development projects."),
    ("Improved disaster preparedness and response.", "Enhancing resilience to extreme weather events through early warning systems and emergency planning."),
    ("Better waste management and recycling programs.", "Encouraging sustainable waste disposal practices to reduce environmental impact."),
    ("Equitable access to public transportation.", "Ensuring all community members can travel affordably and sustainably."),
    ("Safer walking and biking infrastructure.", "Promoting pedestrian-friendly streets and dedicated bike lanes."),
    ("Increased investment in sustainable agriculture.", "Supporting local food production and reducing dependence on imported goods."),
    ("Affordable and accessible renewable energy.", "Ensuring clean energy options are available to all residents."),
    ("Preservation of cultural heritage in urban planning.", "Respecting historical sites while implementing modern sustainability measures."),
    ("Better indoor air quality in residential and commercial buildings.", "Enhancing health and well-being by reducing indoor pollutants."),
    ("Stronger regulations on industrial pollution.", "Reducing toxic emissions from factories and improving local air quality."),
    ("Community-led conservation efforts.", "Empowering local groups to protect natural habitats and biodiversity."),
    ("Encouraging local businesses to adopt sustainable practices.", "Promoting eco-friendly commerce and reducing carbon footprints."),
    ("Greater transparency in environmental reporting.", "Ensuring the public has access to accurate and timely data on climate initiatives."),
    ("Better stormwater management to reduce flooding.", "Protecting homes and infrastructure from extreme weather events."),
    ("More funding for climate education programs.", "Raising awareness about sustainability and climate change solutions."),
    ("Improved social equity in climate adaptation planning.", "Ensuring vulnerable populations receive necessary support."),
    ("Public-private partnerships for sustainability projects.", "Encouraging businesses and governments to collaborate on green initiatives."),
    ("Expansion of urban forests and tree planting programs.", "Providing shade, reducing heat islands, and improving air quality."),
    ("Integration of climate change resilience into local policies.", "Ensuring long-term sustainability in governance."),
    ("Greater focus on climate justice and fair policy implementation.", "Addressing inequalities in climate impact distribution."),
    ("Encouraging youth involvement in climate advocacy.", "Providing young people with a platform to engage in environmental activism."),
    ("Increased support for indigenous land and resource rights.", "Recognizing traditional knowledge and sustainable land stewardship."),
    ("More accessible funding for small-scale sustainability initiatives.", "Empowering communities to take local action on climate challenges."),
    ("Development of sustainable tourism practices.", "Reducing environmental degradation while supporting local economies."),
    ("Better coordination between municipalities and environmental agencies.", "Ensuring alignment in sustainability goals and enforcement."),
    ("Strengthening food security through climate-resilient crops.", "Encouraging agricultural practices that withstand extreme weather."),
    ("Expansion of rooftop solar panel programs.", "Making renewable energy more accessible for households."),
    ("Incentives for green businesses and startups.", "Encouraging entrepreneurs to invest in sustainable solutions."),
    ("More affordable energy-efficient appliances.", "Helping consumers reduce energy consumption and costs."),
    ("Greater investment in climate-adaptive infrastructure.", "Ensuring roads, bridges, and buildings can withstand climate impacts."),
    ("Reduction of single-use plastics.", "Encouraging reusable alternatives to minimize waste."),
    ("Support for energy-efficient public buildings.", "Reducing government energy consumption and promoting sustainability."),
    ("Strengthened noise pollution regulations.", "Creating quieter and more livable urban environments."),
    ("Improved wildlife conservation efforts.", "Protecting ecosystems and endangered species."),
    ("More sustainable fishing and marine conservation policies.", "Preserving ocean health while supporting coastal communities."),
    ("Increased availability of electric vehicle charging stations.", "Promoting the transition to cleaner transportation."),
    ("Better incentives for community-led renewable energy projects.", "Encouraging grassroots participation in sustainable energy production."),
    ("Support for circular economy initiatives.", "Reducing waste through reuse, repair, and recycling."),
    ("Improved management of urban wetlands and natural water bodies.", "Enhancing biodiversity and flood mitigation."),
    ("Stronger policies on chemical pollution in agriculture.", "Reducing harmful pesticides and protecting soil health."),
    ("Investment in sustainable urban design and smart cities.", "Using technology to improve energy efficiency and reduce waste."),
    ("Community-led initiatives to tackle climate change.", "Empowering grassroots organizations to take local action."),
    ("Increased transparency in corporate environmental responsibility.", "Holding businesses accountable for their climate impact."),
    ("Improved community-led air and water quality monitoring.", "Providing real-time data for better decision-making."),
    ("More green infrastructure in underserved communities.", "Ensuring environmental justice for all residents."),
    ("Equitable disaster relief and recovery planning.", "Ensuring all communities receive adequate post-disaster support."),
    ("Climate-conscious urban redevelopment programs.", "Retrofitting older buildings to improve energy efficiency."),
    ("Enhanced biodiversity corridors in urban spaces.", "Providing safe habitats for wildlife in cities."),
    ("More community gardens and urban farming initiatives.", "Promoting local food production and reducing carbon footprints."),
    ("Improved recycling and composting access for residents.", "Making waste reduction more convenient."),
    ("Expansion of affordable and sustainable housing options.", "Ensuring climate-friendly homes are accessible to all."),
    ("Promotion of sustainable fashion and textiles.", "Reducing waste and pollution from the fashion industry."),
    ("Increased funding for environmental health research.", "Better understanding and addressing pollution-related health issues."),
    ("Sustainable school programs and climate education curricula.", "Educating the next generation about environmental responsibility."),
    ("More local government support for climate startups.", "Helping innovators develop and scale green technologies."),
    ("Expansion of net-zero emission building policies.", "Encouraging low-carbon construction and design."),
    ("Strengthened legal frameworks for environmental protection.", "Ensuring stronger enforcement of climate regulations."),
    ("Greater investment in low-carbon transportation solutions.", "Reducing greenhouse gas emissions from transit systems."),
    ("Stronger protection of rivers, lakes, and coastal areas.", "Ensuring the health of vital water resources."),
    ("More research into sustainable materials and alternatives.", "Developing eco-friendly replacements for traditional materials."),
    ("Encouragement of community-led climate activism.", "Providing resources for local groups to advocate for climate action."),
    ("Development of city-wide climate action plans.", "Establishing long-term goals for reducing emissions."),
    ("Improved social resilience through climate adaptation training.", "Equipping communities with knowledge to handle climate risks."),
    ("Encouragement of carbon offset programs for residents and businesses.", "Helping individuals and organizations neutralize their emissions."),
    ("More sustainable financing options for homeowners and businesses.", "Providing green loans and grants for sustainability projects."),
    ("Better data collection and public reporting on climate progress.", "Ensuring accountability and transparency in sustainability efforts."),
]





from transformers import pipeline

# Load pre-trained transformer pipeline
classifier = pipeline("zero-shot-classification")

# Define lists for each category


# Function to find the top matches
def find_top_matches(project_description, category_items, top_n=3):
    scored_items = []
    for item, description in category_items:
        result = classifier(project_description, candidate_labels=[item])
        score = result['scores'][0]
        scored_items.append((item, description, score))

    scored_items.sort(key=lambda x: x[2], reverse=True)
    return scored_items[:top_n]

# Function to assess a project recommendation
def assess_project_recommendation():
    project_description = input("Enter your project recommendation: ")

    results = {
        "Top Coordination Mechanisms": find_top_matches(project_description, coordination_mechanisms),
        "Top Processes": find_top_matches(project_description, processes),
        "Top Improved Project Resilience Factors": find_top_matches(project_description, improved_project_resilience),
        "Top Community Satisfaction Factors": find_top_matches(project_description, community_satisfaction),
        "Top Monitoring Indicators": find_top_matches(project_description, monitoring_indicators),
        "Top Feedback Mechanisms": find_top_matches(project_description, feedback_mechanisms),
        "Top Governence rules": find_top_matches(project_description, governance_rules),
        "Top Socio-ecological Challenges": find_top_matches(project_description, socio_ecological_challenges),
        "Top Stakeholders": find_top_matches(project_description, stakeholders),
    }

    for category, items in results.items():
        print(f"\n{category}:")
        for item, description, score in items:
            print(f" - {item}: {description} (Relevance Score: {score:.2f})")

# Run the assessment
assess_project_recommendation()




!pip install pymupdf sentence-transformers torch

import fitz  # PyMuPDF for PDF text extraction
import torch
from sentence_transformers import SentenceTransformer, util
from google.colab import files

# Load pre-trained sentence embedding model
model = SentenceTransformer('all-MiniLM-L6-v2')

def extract_text_with_location(pdf_path):
    """
    Extracts text from a PDF file while tracking page numbers and line numbers.
    Returns a list of tuples: (sentence, full_line, page_number, line_number)
    """
    doc = fitz.open(pdf_path)
    sentences_with_location = []

    for page_num in range(len(doc)):
        page = doc[page_num]
        text = page.get_text("text")  # Extract text from the page
        lines = text.split("\n")  # Split into individual lines

        for line_num, line in enumerate(lines, start=1):
            sentences = line.split(". ")  # Simple sentence split
            for sentence in sentences:
                if sentence.strip():  # Avoid empty sentences
                    sentences_with_location.append((sentence.strip(), line.strip(), page_num + 1, line_num))

    return sentences_with_location

def find_similar_sentences(sentences_with_location, query, top_n=3):
    """
    Finds the most similar sentences to the query and returns their locations with full lines.
    """
    sentences = [entry[0] for entry in sentences_with_location]  # Extract only sentences
    sentence_embeddings = model.encode(sentences, convert_to_tensor=True)
    query_embedding = model.encode(query, convert_to_tensor=True)

    # Compute cosine similarity
    similarities = util.pytorch_cos_sim(query_embedding, sentence_embeddings)[0]
    top_results = torch.topk(similarities, top_n)

    # Retrieve matching sentences with page and line numbers
    top_matches = []
    for idx in top_results.indices:
        sentence, full_line, page_num, line_num = sentences_with_location[idx]
        top_matches.append((sentence, full_line, page_num, line_num))

    return top_matches

# Upload PDF file
uploaded = files.upload()
pdf_path = list(uploaded.keys())[0]  # Get the uploaded file name

# Extract text with location info
sentences_with_location = extract_text_with_location(pdf_path)

# Get user input for the query
query_word = input("Enter the word or phrase you want to search: ").strip()

# Find similar sentences
similar_sentences = find_similar_sentences(sentences_with_location, query_word, top_n=4)

# Display results
if similar_sentences:
    print(f"\nTop sentences related to '{query_word}':\n")
    for i, (sentence, full_line, page, line) in enumerate(similar_sentences, 1):
        print(f"{i}. Page {page}, Line {line}:")
        print(f"   Full Line: {full_line}")
        print(f"   Matched Sentence: {sentence}\n")
else:
    print(f"\nNo related sentences found for '{query_word}'.")

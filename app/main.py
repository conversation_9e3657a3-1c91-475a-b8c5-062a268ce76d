import signal
import sys
import threading
from loguru import logger

from app.config import settings
from app.messaging import mq
from app.models import DataMessage, ProcessedResult, BaseEvent, EventType
from app.processors import data_processor, analytics_processor


class RMTService:
    
    
    def __init__(self):
        self.running = True
        self.setup_logging()
    
    def setup_logging(self):
        """Configure logging"""
        logger.remove()
        logger.add(
            sys.stdout,
            level=settings.log_level,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
        )
    
    def handle_event(self, message_data: dict):
        """Handle incoming events from single queue"""
        try:
            # Check if it's a BaseEvent or direct data
            if 'event_type' in message_data:
                # It's a BaseEvent
                event = BaseEvent(**message_data)
                event_type = event.event_type
                payload = event.payload
                logger.info(f"Received event: {event_type}")
            else:
                # It's direct data (for backward compatibility)
                event_type = "data_received"
                payload = message_data
                logger.info("Received direct data message")

            # Route based on event type
            if event_type == EventType.DATA_RECEIVED or event_type == "data_received":
                self._handle_data_received(payload)
            elif event_type == EventType.DATA_PROCESSED:
                self._handle_data_processed(payload)
            elif event_type == EventType.ANALYTICS_GENERATED:
                self._handle_analytics_generated(payload)
            elif event_type == EventType.NOTIFICATION:
                self._handle_notification(payload)
            else:
                logger.warning(f"Unknown event type: {event_type}")

        except Exception as e:
            logger.error(f"Error handling event: {e}")

    def _handle_data_received(self, payload: dict):
        """Handle raw data received event"""
        try:
            message = DataMessage(**payload)
            logger.info(f"Processing raw data: {message.data_type}")

            # Process the data
            result = data_processor.process_data(message)

            # Send to analytics
            analytics_processor.aggregate_results(result)

        except Exception as e:
            logger.error(f"Error handling raw data: {e}")

    def _handle_data_processed(self, payload: dict):
        """Handle processed data event"""
        try:
            result = ProcessedResult(**payload)
            logger.info(f"Received processed data: {result.data_type}")
            analytics_processor.aggregate_results(result)
        except Exception as e:
            logger.error(f"Error handling processed data: {e}")

    def _handle_analytics_generated(self, payload: dict):
        """Handle analytics report event"""
        logger.info(f"Analytics report generated: {payload.get('report_id')}")

    def _handle_notification(self, payload: dict):
        """Handle notification event"""
        level = payload.get('level', 'INFO')
        message = payload.get('message', '')
        logger.info(f"Notification [{level}]: {message}")
    
    def start_consumers(self):
        """Start message consumer for single queue"""
        thread = threading.Thread(
            target=self._consume_queue,
            args=(settings.main_queue, self.handle_event),
            daemon=True
        )
        thread.start()
        logger.info(f"Started consumer for {settings.main_queue}")

        return [thread]
    
    def _consume_queue(self, queue: str, handler):
        """Consume messages from a specific queue"""
        try:
            mq.consume(queue, handler)
        except Exception as e:
            logger.error(f"Error in consumer for {queue}: {e}")
    
    def run(self):
        """Run the service"""
        logger.info("Starting RMT Service")
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        # Start consumers
        threads = self.start_consumers()
        
        logger.info("Service is running. Press Ctrl+C to stop.")
        
        # Keep main thread alive
        try:
            while self.running:
                for thread in threads:
                    thread.join(timeout=1.0)
        except KeyboardInterrupt:
            pass
        
        self.shutdown()
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"Received signal {signum}, shutting down...")
        self.running = False
    
    def shutdown(self):
        """Shutdown the service"""
        logger.info("Shutting down RMT Service")
        mq.close()


if __name__ == "__main__":
    service = RMTService()
    service.run()

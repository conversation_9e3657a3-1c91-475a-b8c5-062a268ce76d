import signal
import sys
import threading
from loguru import logger

from app.config import settings
from app.messaging import mq
from app.models import DataMessage, ProcessedResult
from app.processors import data_processor, analytics_processor


class RMTService:
    
    
    def __init__(self):
        self.running = True
        self.setup_logging()
    
    def setup_logging(self):
        """Configure logging"""
        logger.remove()
        logger.add(
            sys.stdout,
            level=settings.log_level,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
        )
    
    def handle_raw_data(self, message_data: dict):
        """Handle incoming raw data"""
        try:
            message = DataMessage(**message_data)
            logger.info(f"Received raw data: {message.data_type}")
            
            # Process the data
            result = data_processor.process_data(message)
            
            # Send to analytics
            analytics_processor.aggregate_results(result)
            
        except Exception as e:
            logger.error(f"Error handling raw data: {e}")
    
    def handle_processed_data(self, message_data: dict):
        """Handle processed data for analytics"""
        try:
            result = ProcessedResult(**message_data)
            logger.info(f"Received processed data: {result.data_type}")
            analytics_processor.aggregate_results(result)
        except Exception as e:
            logger.error(f"Error handling processed data: {e}")
    
    def handle_analytics(self, message_data: dict):
        """Handle analytics reports"""
        logger.info(f"Analytics report generated: {message_data.get('report_id')}")
    
    def handle_notifications(self, message_data: dict):
        """Handle notification messages"""
        level = message_data.get('level', 'INFO')
        message = message_data.get('message', '')
        logger.info(f"Notification [{level}]: {message}")
    
    def start_consumers(self):
        """Start message consumers in separate threads"""
        consumers = [
            (settings.raw_data_queue, self.handle_raw_data),
            (settings.processed_data_queue, self.handle_processed_data),
            (settings.analytics_queue, self.handle_analytics),
            (settings.notifications_queue, self.handle_notifications)
        ]
        
        threads = []
        for queue, handler in consumers:
            thread = threading.Thread(
                target=self._consume_queue,
                args=(queue, handler),
                daemon=True
            )
            thread.start()
            threads.append(thread)
            logger.info(f"Started consumer for {queue}")
        
        return threads
    
    def _consume_queue(self, queue: str, handler):
        """Consume messages from a specific queue"""
        try:
            mq.consume(queue, handler)
        except Exception as e:
            logger.error(f"Error in consumer for {queue}: {e}")
    
    def run(self):
        """Run the service"""
        logger.info("Starting Climate Governance Service")
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        # Start consumers
        threads = self.start_consumers()
        
        logger.info("Service is running. Press Ctrl+C to stop.")
        
        # Keep main thread alive
        try:
            while self.running:
                for thread in threads:
                    thread.join(timeout=1.0)
        except KeyboardInterrupt:
            pass
        
        self.shutdown()
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"Received signal {signum}, shutting down...")
        self.running = False
    
    def shutdown(self):
        """Shutdown the service"""
        logger.info("Shutting down Climate Governance Service")
        mq.close()


if __name__ == "__main__":
    service = RMTService()
    service.run()

from pydantic_settings import BaseSettings
from typing import Optional


class Settings(BaseSettings):
    rabbitmq_url: str = "amqp://guest:<EMAIL>:5672/"
    log_level: str = "INFO"
    
    # Queue names
    raw_data_queue: str = "climate.raw.data"
    processed_data_queue: str = "climate.processed.data"
    analytics_queue: str = "climate.analytics"
    notifications_queue: str = "climate.notifications"
    
    # Exchange name
    exchange_name: str = "climate.exchange"
    
    class Config:
        env_file = ".env"


settings = Settings()

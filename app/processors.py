import uuid
from datetime import datetime
from typing import List, Dict, Any
from loguru import logger
import pandas as pd

from app.models import (
    DataMessage, ProcessedResult, AnalyticsReport,
    NotificationMessage, DataType, DataItem, BaseEvent, EventType
)
from app.messaging import mq
from app.config import settings


class DataProcessor:
    """Base processor for climate data"""

    def process_data(self, message: DataMessage) -> ProcessedResult:
        """Process climate data and return results"""
        logger.info(f"Processing {message.data_type} with {len(message.items)} items")
        
        # Convert to DataFrame for analysis
        df = pd.DataFrame([item.dict() for item in message.items])
        
        # Generate insights based on data type
        insights = self._generate_insights(df, message.data_type)
        
        # Create summary
        summary = self._create_summary(df, message.data_type)
        
        result = ProcessedResult(
            message_id=message.message_id,
            timestamp=datetime.now(),
            data_type=message.data_type,
            total_items=len(message.items),
            processed_items=len(message.items),
            insights=insights,
            summary=summary
        )
        
        # Publish processed result as event
        processed_event = BaseEvent(
            event_id=str(uuid.uuid4()),
            event_type=EventType.DATA_PROCESSED,
            timestamp=datetime.now(),
            payload=result.dict()
        )
        mq.publish("rmt.data_processed", processed_event.dict())

        # Send notification
        self._send_notification(f"Processed {len(message.items)} {message.data_type} items")
        
        return result
    
    def _generate_insights(self, df: pd.DataFrame, data_type: DataType) -> List[str]:
        """Generate insights based on data type"""
        insights = []
        
        if data_type == DataType.COORDINATION_MECHANISMS:
            insights.append(f"Found {len(df)} coordination mechanisms")
            if 'name' in df.columns:
                insights.append(f"Most common mechanism type: {df['name'].str.split().str[0].mode().iloc[0] if not df.empty else 'N/A'}")
        
        elif data_type == DataType.PROCESSES:
            insights.append(f"Analyzed {len(df)} climate processes")
            insights.append("Focus areas include integration, consultation, and assessment")
        
        elif data_type == DataType.FEEDBACK_MECHANISMS:
            insights.append(f"Evaluated {len(df)} feedback mechanisms")
            insights.append("Emphasis on monitoring, stakeholder engagement, and transparency")
        
        elif data_type == DataType.MONITORING_INDICATORS:
            insights.append(f"Processed {len(df)} monitoring indicators")
            insights.append("Key metrics include emissions reduction, compliance, and community satisfaction")
        
        elif data_type == DataType.COMMUNITY_SATISFACTION:
            insights.append(f"Analyzed {len(df)} community satisfaction factors")
            insights.append("Focus on transparency, participation, and environmental quality")
        
        elif data_type == DataType.PROJECT_RESILIENCE:
            insights.append(f"Assessed {len(df)} project resilience factors")
            insights.append("Emphasis on climate adaptation, energy efficiency, and sustainability")
        
        return insights
    
    def _create_summary(self, df: pd.DataFrame, data_type: DataType) -> Dict[str, Any]:
        """Create summary statistics"""
        return {
            "total_count": len(df),
            "data_type": data_type,
            "avg_description_length": df['description'].str.len().mean() if 'description' in df.columns else 0,
            "unique_categories": df['category'].nunique() if 'category' in df.columns else 0,
            "processed_at": datetime.now().isoformat()
        }
    
    def _send_notification(self, message: str):
        """Send notification message"""
        notification = NotificationMessage(
            notification_id=str(uuid.uuid4()),
            timestamp=datetime.now(),
            level="INFO",
            message=message
        )

        notification_event = BaseEvent(
            event_id=str(uuid.uuid4()),
            event_type=EventType.NOTIFICATION,
            timestamp=datetime.now(),
            payload=notification.dict()
        )
        mq.publish("rmt.notification", notification_event.dict())


class AnalyticsProcessor:
    """Aggregates processed data and generates reports"""
    
    def __init__(self):
        self.processed_data = []
    
    def aggregate_results(self, result: ProcessedResult):
        """Aggregate processed results"""
        self.processed_data.append(result)
        logger.info(f"Aggregated result for {result.data_type}")
        
        # Generate report if we have enough data
        if len(self.processed_data) >= 3:  # Arbitrary threshold
            self._generate_report()
    
    def _generate_report(self):
        """Generate analytics report"""
        total_items = sum(r.processed_items for r in self.processed_data)
        data_types = list(set(r.data_type for r in self.processed_data))
        
        # Combine insights
        all_insights = []
        for result in self.processed_data:
            all_insights.extend(result.insights)
        
        # Generate recommendations
        recommendations = [
            "Implement integrated climate governance framework",
            "Enhance community participation in decision-making",
            "Strengthen monitoring and feedback mechanisms",
            "Prioritize nature-based solutions",
            "Improve cross-sector coordination"
        ]
        
        report = AnalyticsReport(
            report_id=str(uuid.uuid4()),
            timestamp=datetime.now(),
            data_types=data_types,
            total_items=total_items,
            key_insights=all_insights[:10],  # Top 10 insights
            recommendations=recommendations,
            metrics={
                "total_processed": total_items,
                "data_types_count": len(data_types),
                "processing_efficiency": 100.0  # Simplified metric
            }
        )
        
        # Publish report as event
        analytics_event = BaseEvent(
            event_id=str(uuid.uuid4()),
            event_type=EventType.ANALYTICS_GENERATED,
            timestamp=datetime.now(),
            payload=report.dict()
        )
        mq.publish("rmt.analytics", analytics_event.dict())
        
        # Clear processed data
        self.processed_data = []
        
        logger.info(f"Generated analytics report: {report.report_id}")


# Global processor instances
data_processor = DataProcessor()
analytics_processor = AnalyticsProcessor()

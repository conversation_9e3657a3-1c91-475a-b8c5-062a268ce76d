import pika
import json
import time
from typing import Callable, Any, Dict
from loguru import logger
from app.config import settings


class MessageQueue:
    def __init__(self):
        self.connection = None
        self.channel = None
        self.connect()
    
    def connect(self):
        """Connect to RabbitMQ with retry logic"""
        max_retries = 5
        retry_delay = 5
        
        for attempt in range(max_retries):
            try:
                self.connection = pika.BlockingConnection(
                    pika.URLParameters(settings.rabbitmq_url)
                )
                self.channel = self.connection.channel()
                self.setup_queues()
                logger.info("Connected to RabbitMQ successfully")
                return
            except Exception as e:
                logger.warning(f"Failed to connect to RabbitMQ (attempt {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                else:
                    raise
    
    def setup_queues(self):
        """Setup exchanges and queues"""
        # Declare exchange
        self.channel.exchange_declare(
            exchange=settings.exchange_name,
            exchange_type='topic',
            durable=True
        )

        # Declare single main queue
        self.channel.queue_declare(queue=settings.main_queue, durable=True)
        self.channel.queue_bind(
            exchange=settings.exchange_name,
            queue=settings.main_queue,
            routing_key="rmt.*"
        )
    
    def publish(self, routing_key: str, message: Dict[str, Any]):
        """Publish a message to the exchange"""
        try:
            self.channel.basic_publish(
                exchange=settings.exchange_name,
                routing_key=routing_key,
                body=json.dumps(message),
                properties=pika.BasicProperties(
                    delivery_mode=2,  # Make message persistent
                    content_type='application/json'
                )
            )
            logger.debug(f"Published message to {routing_key}")
        except Exception as e:
            logger.error(f"Failed to publish message: {e}")
            raise
    
    def consume(self, queue: str, callback: Callable):
        """Consume messages from a queue"""
        def wrapper(ch, method, properties, body):
            try:
                message = json.loads(body)
                logger.debug(f"Received message from {queue}")
                callback(message)
                ch.basic_ack(delivery_tag=method.delivery_tag)
            except Exception as e:
                logger.error(f"Error processing message: {e}")
                ch.basic_nack(delivery_tag=method.delivery_tag, requeue=False)
        
        self.channel.basic_qos(prefetch_count=1)
        self.channel.basic_consume(queue=queue, on_message_callback=wrapper)
        logger.info(f"Started consuming from {queue}")
        self.channel.start_consuming()
    
    def close(self):
        """Close the connection"""
        if self.connection and not self.connection.is_closed:
            self.connection.close()
            logger.info("Closed RabbitMQ connection")


# Global message queue instance
mq = MessageQueue()

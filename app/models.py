from pydantic import BaseModel
from typing import List, Dict, Any, Optional
from datetime import datetime
from enum import Enum


class EventType(str, Enum):
    DATA_RECEIVED = "data_received"
    DATA_PROCESSED = "data_processed"
    ANALYTICS_GENERATED = "analytics_generated"
    NOTIFICATION = "notification"


class DataType(str, Enum):
    COORDINATION_MECHANISMS = "coordination_mechanisms"
    PROCESSES = "processes"
    FEEDBACK_MECHANISMS = "feedback_mechanisms"
    MONITORING_INDICATORS = "monitoring_indicators"
    COMMUNITY_SATISFACTION = "community_satisfaction"
    PROJECT_RESILIENCE = "project_resilience"


class DataItem(BaseModel):
    name: str
    description: str
    category: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class BaseEvent(BaseModel):
    event_id: str
    event_type: EventType
    timestamp: datetime
    payload: Dict[str, Any]


class DataMessage(BaseModel):
    message_id: str
    timestamp: datetime
    data_type: DataType
    items: List[DataItem]
    source: Optional[str] = None


class ProcessedResult(BaseModel):
    message_id: str
    timestamp: datetime
    data_type: DataType
    total_items: int
    processed_items: int
    insights: List[str]
    summary: Dict[str, Any]


class AnalyticsReport(BaseModel):
    report_id: str
    timestamp: datetime
    data_types: List[DataType]
    total_items: int
    key_insights: List[str]
    recommendations: List[str]
    metrics: Dict[str, Any]


class NotificationMessage(BaseModel):
    notification_id: str
    timestamp: datetime
    level: str  # INFO, WARNING, ERROR
    message: str
    details: Optional[Dict[str, Any]] = None

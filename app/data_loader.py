import uuid
import re
from datetime import datetime
from typing import List, <PERSON><PERSON>
from loguru import logger

from app.models import DataMessage, DataItem, DataType, BaseEvent, EventType
from app.messaging import mq
from app.config import settings


class RMTDataLoader:
    """Loads climate data from colab.md and sends to message queues"""
    
    def __init__(self, file_path: str = "colab.md"):
        self.file_path = file_path
    
    def parse_data_section(self, content: str, variable_name: str) -> List[Tuple[str, str]]:
        """Parse a data section from the file"""
        pattern = rf'{variable_name}\s*=\s*\[(.*?)\]'
        match = re.search(pattern, content, re.DOTALL)
        
        if not match:
            return []
        
        section_content = match.group(1)
        
        # Extract tuples
        tuple_pattern = r'\("([^"]+)",\s*"([^"]+)"\)'
        tuples = re.findall(tuple_pattern, section_content)
        
        return tuples
    
    def load_and_send_data(self):
        """Load data from file and send to message queues"""
        try:
            with open(self.file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            # Define data sections to extract
            sections = {
                'coordination_mechanisms': DataType.COORDINATION_MECHANISMS,
                'processes': DataType.PROCESSES,
                'feedback_mechanisms': DataType.FEEDBACK_MECHANISMS,
                'monitoring_indicators': DataType.MONITORING_INDICATORS,
                'community_satisfaction': DataType.COMMUNITY_SATISFACTION,
                'improved_project_resilience': DataType.PROJECT_RESILIENCE
            }
            
            for variable_name, data_type in sections.items():
                logger.info(f"Processing {variable_name}")
                
                # Parse data
                tuples = self.parse_data_section(content, variable_name)
                
                if not tuples:
                    logger.warning(f"No data found for {variable_name}")
                    continue
                
                # Convert to DataItem objects
                items = []
                for name, description in tuples:
                    item = DataItem(
                        name=name,
                        description=description,
                        category=data_type.value
                    )
                    items.append(item)

                # Create message
                message = DataMessage(
                    message_id=str(uuid.uuid4()),
                    timestamp=datetime.now(),
                    data_type=data_type,
                    items=items,
                    source="colab.md"
                )

                # Create event
                event = BaseEvent(
                    event_id=str(uuid.uuid4()),
                    event_type=EventType.DATA_RECEIVED,
                    timestamp=datetime.now(),
                    payload=message.dict()
                )

                # Send to single queue
                mq.publish("rmt.data_received", event.dict())
                logger.info(f"Sent {len(items)} {data_type} items to queue")
        
        except Exception as e:
            logger.error(f"Error loading data: {e}")
            raise


if __name__ == "__main__":
    loader = RMTDataLoader()
    loader.load_and_send_data()
    logger.info("Data loading completed")

import uuid
from datetime import datetime
from loguru import logger

from app.models import DataMessage, DataItem, DataType, BaseEvent, EventType
from app.messaging import mq

# Import data from Python files
from data import (
    coordination_mechanisms,
    processes,
    feedback_mechanisms,
    monitoring_indicators,
    community_satisfaction,
    improved_project_resilience,
    stakeholders,
    socio_ecological_challenges,
    governance_rules
)


class RMTDataLoader:
    """Loads climate data from Python data files and sends to message queues"""

    def __init__(self):
        pass
    
    def get_data_sections(self):
        """Get all data sections from imported Python modules"""
        return {
            'coordination_mechanisms': (coordination_mechanisms, DataType.COORDINATION_MECHANISMS),
            'processes': (processes, DataType.PROCESSES),
            'feedback_mechanisms': (feedback_mechanisms, DataType.FEEDBACK_MECHANISMS),
            'monitoring_indicators': (monitoring_indicators, DataType.MONITORING_INDICATORS),
            'community_satisfaction': (community_satisfaction, DataType.COMMUNITY_SATISFACTION),
            'improved_project_resilience': (improved_project_resilience, DataType.PROJECT_RESILIENCE),
            'stakeholders': (stakeholders, DataType.COORDINATION_MECHANISMS),  # Stakeholders can be coordination
            'socio_ecological_challenges': (socio_ecological_challenges, DataType.MONITORING_INDICATORS),  # Challenges as indicators
            'governance_rules': (governance_rules, DataType.PROCESSES),  # Rules as processes
        }
    
    def load_and_send_data(self):
        """Load data from Python files and send to message queues"""
        try:
            # Get all data sections
            data_sections = self.get_data_sections()

            for section_name, (data_list, data_type) in data_sections.items():
                logger.info(f"Processing {section_name}")

                if not data_list:
                    logger.warning(f"No data found for {section_name}")
                    continue

                # Convert to DataItem objects
                items = []
                for name, description in data_list:
                    item = DataItem(
                        name=name,
                        description=description,
                        category=data_type.value
                    )
                    items.append(item)

                # Create message
                message = DataMessage(
                    message_id=str(uuid.uuid4()),
                    timestamp=datetime.now(),
                    data_type=data_type,
                    items=items,
                    source=f"data/{section_name}.py"
                )

                # Create event
                event = BaseEvent(
                    event_id=str(uuid.uuid4()),
                    event_type=EventType.DATA_RECEIVED,
                    timestamp=datetime.now(),
                    payload=message.dict()
                )

                # Send to single queue
                mq.publish("rmt.data_received", event.dict())
                logger.info(f"Sent {len(items)} {data_type.value} items to queue")

        except Exception as e:
            logger.error(f"Error loading data: {e}")
            raise


if __name__ == "__main__":
    loader = RMTDataLoader()
    loader.load_and_send_data()
    logger.info("Data loading completed")

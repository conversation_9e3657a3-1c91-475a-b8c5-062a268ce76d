#!/usr/bin/env python3
"""
Simple test script to verify the climate service is working
"""

import time
import uuid
from datetime import datetime
from app.models import DataMessage, DataItem, DataType, BaseEvent, EventType
from app.messaging import mq
from app.config import settings


def test_service():
    """Send test data to verify service is working"""
    print("Testing Climate Governance Service...")
    
    # Create test data
    test_items = [
        ClimateDataItem(
            name="Test Community Decision Center",
            description="A test mechanism for community engagement in climate decisions.",
            category="test"
        ),
        ClimateDataItem(
            name="Test Climate Advisory Board",
            description="A test board providing climate expertise and guidance.",
            category="test"
        )
    ]
    
    # Create test message
    test_message = ClimateDataMessage(
        message_id=str(uuid.uuid4()),
        timestamp=datetime.now(),
        data_type=DataType.COORDINATION_MECHANISMS,
        items=test_items,
        source="test_script"
    )
    
    # Send test message
    print(f"Sending test message with {len(test_items)} items...")
    mq.publish(settings.raw_data_queue, test_message.dict())
    print("Test message sent successfully!")
    
    print("\nCheck the service logs to see processing results.")
    print("You can also check RabbitMQ management UI at http://localhost:15672")


if __name__ == "__main__":
    test_service()
